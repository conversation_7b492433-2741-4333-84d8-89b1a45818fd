<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:textAppearance="?android:attr/textAppearanceLarge"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:id="@+id/textView1" />
    <RadioGroup
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <RadioButton
            android:id="@+id/radioPriorityLow"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Low"
            android:checked="true" />
        <RadioButton
            android:id="@+id/radioPriorityMedium"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Medium" />
        <RadioButton
            android:id="@+id/radioPriorityHigh"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="High" />
    </RadioGroup>

    <Button
        android:text="Add Item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/buttonAddItem" />
    <EditText
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/editTextTitle"
        android:hint="Enter item title"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:imeOptions="actionGo" />
</LinearLayout>
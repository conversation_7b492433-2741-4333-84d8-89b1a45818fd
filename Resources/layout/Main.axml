<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_weight="1"
    android:gravity="bottom">
    <Button
        android:text="Backup Database"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/buttonBackupDB" />
    <Button
        android:text="Restore Database"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/buttonRestoreDB" />
    <Button
        android:text="Delete Database"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/buttonDeleteDB" />
    <Button
        android:text="Register for Calendar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/buttonRegister" />
    <Button
        android:text="Active"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/buttonActiveReminders" />
    <Button
        android:text="Recent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/buttonRecentReminders" />
    <Button
        android:text="Recently Dismissed"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/buttonRecentlyDismissed" />
    <Button
        android:text="View Log"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/buttonViewLog" />
    <Button
        android:text="New Reminder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/buttonCreateItem"
        android:textColor="@android:color/holo_blue_bright" />
    <TextView
        android:text="Output"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/textViewOutput" />
</LinearLayout>
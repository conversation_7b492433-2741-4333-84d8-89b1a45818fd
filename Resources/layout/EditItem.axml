<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:text="Todo Item description"
        android:layout_width="match_parent"
        android:layout_height="fill_parent"
        android:layout_weight="1"
        android:id="@+id/textViewDescription" />
    <RadioGroup
        android:id="@+id/radioGroupPriority"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <RadioButton
            android:id="@+id/radioPriorityLow"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Low"
            android:checked="true" />
        <RadioButton
            android:id="@+id/radioPriorityMedium"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Medium" />
        <RadioButton
            android:id="@+id/radioPriorityHigh"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="High" />
    </RadioGroup>
    <Button
        android:text="10 Seconds"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:tag="10"
        android:id="@+id/button10seconds" />
    <Button
        android:text="5 Minutes"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:tag="300"
        android:id="@+id/button5mins" />
    <Button
        android:text="15 Minutes"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:tag="900"
        android:id="@+id/button15minutes" />
    <Button
        android:text="30 Minutes"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:tag="1800"
        android:id="@+id/button30minutes" />
    <Button
        android:text="1 Hour"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:tag="3600"
        android:id="@+id/button1hour" />
    <Button
        android:text="1 Day"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:tag="86400"
        android:id="@+id/button1day" />
    <Button
        android:text="Specify Date and Time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/buttonDateTime" />
    <Button
        android:text="--Dismiss--"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/buttonDismiss" />
</LinearLayout>
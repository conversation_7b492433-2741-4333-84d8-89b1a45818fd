using Android.App;
using Android.Content;
using Android.Database;
using Android.OS;
using Android.Provider;
using Android.Widget;
using System;

namespace nz.co.haroco.reminderplus
{
    [Activity(MainLauncher = true, Icon = "@mipmap/rp_launcher")]
    public class MainActivity : Activity
    {
        private TextView tvOutput;

        protected override void OnCreate(Bundle bundle)
        {
            base.OnCreate(bundle);

            // Set our view from the "main" layout resource
            SetContentView(Resource.Layout.Main);

            // Get our button from the layout resource,
            // and attach an event to it
            Button buttonCreateItem = FindViewById<Button>(Resource.Id.buttonCreateItem);
            Button buttonBackupDB = FindViewById<Button>(Resource.Id.buttonBackupDB);
            Button buttonRestoreDB = FindViewById<Button>(Resource.Id.buttonRestoreDB);
            Button buttonDeleteDB = FindViewById<Button>(Resource.Id.buttonDeleteDB);
            Button buttonRecentlyDismissed = FindViewById<Button>(Resource.Id.buttonRecentlyDismissed);
            Button buttonViewLog = FindViewById<Button>(Resource.Id.buttonViewLog);
            tvOutput = FindViewById<TextView>(Resource.Id.textViewOutput);

            Button buttonRegister = FindViewById<Button>(Resource.Id.buttonRegister);
            Button buttonActiveReminders = FindViewById<Button>(Resource.Id.buttonActiveReminders);
            Button buttonRecentReminders = FindViewById<Button>(Resource.Id.buttonRecentReminders);

            buttonCreateItem.Click += AddTodoItem;
            buttonBackupDB.Click += BackupDB;
            buttonRestoreDB.Click += RestoreDB;
            buttonDeleteDB.Click += DeleteDB;
            buttonRecentlyDismissed.Click += ShowRecentlyDismissed;
            buttonViewLog.Click += ViewLog;
            buttonRegister.Click += RegisterForCalendarEvents;
            buttonActiveReminders.Click += ShowActiveReminders;
            buttonRecentReminders.Click += ShowRecentReminders;

            if (CheckSelfPermission(Android.Manifest.Permission.WriteCalendar) == Android.Content.PM.Permission.Denied)
            {
                LogJ.Info("WriteCalendar permission requested");
                RequestPermissions(new String[] { Android.Manifest.Permission.WriteCalendar }, 1);
                //Toast toast = Toast.MakeText(Application.Context, "Request WriteCalendar permission", ToastLength.Long);
                //toast.Show();
            }

            if (CheckSelfPermission(Android.Manifest.Permission.SystemAlertWindow) == Android.Content.PM.Permission.Denied)
            {
                LogJ.Info("SystemAlertWindow permission requested");
                RequestPermissions(new String[] { Android.Manifest.Permission.SystemAlertWindow }, 2);
                //Toast toast = Toast.MakeText(Application.Context, "Request SystemAlertWindow permission", ToastLength.Long);
                //toast.Show();
            }
        }

        private void ShowActiveReminders(object sender, EventArgs e)
        {
            var intent = new Intent(this, typeof(RemindersListActivity));
            intent.PutExtra("data", "Active Reminders");
            StartActivity(intent);
        }

        private void ShowRecentReminders(object sender, EventArgs e)
        {
            var intent = new Intent(this, typeof(RemindersListActivity));
            intent.PutExtra("data", "Recent Reminders");
            StartActivity(intent);
        }

        private void ShowRecentlyDismissed(object sender, EventArgs e)
        {
            var intent = new Intent(this, typeof(RemindersListActivity));
            intent.PutExtra("data", "Recently Dismissed Reminders");
            StartActivity(intent);
        }

        private void RegisterForCalendarEvents(object sender, EventArgs e)
        {
            // Check if READ_CALENDAR permission has been granted
            if (CheckSelfPermission(Android.Manifest.Permission.ReadCalendar) == Android.Content.PM.Permission.Granted)
            {
                // We have permission
                var uri = CalendarContract.CalendarAlerts.ContentUri;
                string[] projection = {
                    CalendarContract.CalendarAlertsColumns.EventId,
                    CalendarContract.CalendarAlertsColumns.State,
                    CalendarContract.CalendarAlertsColumns.AlarmTime
                };

                string selection = CalendarContract.CalendarAlertsColumns.AlarmTime + ">" + Util.GetEpochMillisecondsNow();

                var loader = new Android.Content.CursorLoader(this, uri, projection, selection, null, CalendarContract.CalendarAlertsColumns.AlarmTime + " DESC");
                var cursor = (ICursor)loader.LoadInBackground();

                tvOutput.Text = "Results:\n";

                if (cursor.MoveToFirst())
                {
                    do
                    {
                        tvOutput.Append("EventID=" + cursor.GetString(0) + ", State=" + cursor.GetString(1) + ", AlarmTimeLocal=" + Util.GetLocalTimeFromEpoch(cursor.GetLong(2)) + ", AlarmTimeEpoch=" + cursor.GetLong(2) + "\n");
                    } while (cursor.MoveToNext());
                }

                //Temp method for testing whether we get told of calendar events
                var x = new CalendarReminderReceiver();
                IntentFilter inf = new IntentFilter("android.intent.action.EVENT_REMINDER");
                inf.AddDataScheme("content");
                inf.AddDataAuthority("com.android.calendar", null);
                RegisterReceiver(x, inf);
            }
            else
            {
                // Permission is not granted. Request it!
                RequestPermissions(new String[] { Android.Manifest.Permission.ReadCalendar }, 0);
                Toast toast = Toast.MakeText(Application.Context, "Request ReadCalendar permission", ToastLength.Long);
                toast.Show();
            }
        }

        private void BackupDB(object sender, EventArgs e)
        {
            ((ReminderApplication)this.Application).DataManager.BackupDatabase();
        }

        private void RestoreDB(object sender, EventArgs e)
        {
            using var builder = new AlertDialog.Builder(this);
            builder.SetTitle("Are you sure you want to restore the database?");
            builder.SetPositiveButton("Yes", (sender, e) =>
            {
                ((ReminderApplication)this.Application).DataManager.RestoreDatabase();
                ReminderPlusCore.RestoreReminders(this);
            });
            builder.SetNegativeButton("No", (sender, e) => { });
            var dlg = builder.Create();

            dlg.Show();
        }

        private void DeleteDB(object sender, EventArgs e)
        {
            using var builder = new AlertDialog.Builder(this);
            builder.SetTitle("Are you sure you want to delete the database?");
            builder.SetPositiveButton("Yes", (sender, e) => { ((ReminderApplication)this.Application).DataManager.DeleteDatabase(); });
            builder.SetNegativeButton("No", (sender, e) => { });
            var dlg = builder.Create();

            dlg.Show();
        }

        private void AddTodoItem(object sender, EventArgs e)
        {
            StartActivity(typeof(CreateReminderActivity));
        }

        private void ViewLog(object sender, EventArgs e)
        {
            var intent = new Intent(this, typeof(ViewLogActivity));
            intent.PutExtra("data", "Recently Dismissed Reminders");
            StartActivity(intent);
        }
    }
}